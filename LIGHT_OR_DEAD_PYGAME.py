# -*- coding: utf-8 -*-
"""
LIGHT OR DEAD - Pygame Community Edition
Přepsáno z OpenGL verze do čistého pygame-ce
<PERSON><PERSON><PERSON><PERSON> všechny původní funkce s vylepšeným shader systémem
"""

import pygame
import sys
import random
import math
import numpy as np
import time
import os
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

# --- Inicializace Pygame Community Edition ---
pygame.init()
pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)

# --- Konstanty ---
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
WINDOW_TITLE = "Light or Dead (Pygame CE Enhanced)"
FPS = 60
HIGH_SCORE_FILE = "light_or_dead_highscore.txt"

# --- Performance Settings ---
ENABLE_SPATIAL_HASH = True
OBJECT_POOLING = True
RENDER_OPTIMIZATION = True
QUALITY_LEVEL = 1  # 0=Low, 1=Medium, 2=High
VSYNC_ENABLED = True

# --- Trap Settings ---
TRAP_SPIKE_DAMAGE = 15
TRAP_SPIKE_SIZE = 24
TRAP_POISON_DAMAGE = 2
TRAP_POISON_DURATION = 5.0
TRAP_POISON_SIZE = 30
TRAP_SLOW_FACTOR = 0.5
TRAP_SLOW_DURATION = 3.0
TRAP_SLOW_SIZE = 28
TRAP_EXPLOSION_DAMAGE = 25
TRAP_EXPLOSION_SIZE = 32
TRAP_TELEPORT_SIZE = 30
TRAP_SPAWN_CHANCE = 0.2

# --- Konstanty pro upgrady ---
MAX_FIRE_RATE_UPGRADES = 3
MAX_MOVE_SPEED_UPGRADES = 2
MAX_BULLET_DAMAGE_UPGRADES = 4
MAX_LIGHT_RADIUS_UPGRADES = 4
MAX_DASH_DISTANCE_UPGRADES = 3
MAX_DASH_COOLDOWN_UPGRADES = 3
MAX_ARMOR_UPGRADES = 5

# --- Barvy ---
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)
CYAN = (0, 255, 255)
MAGENTA = (255, 0, 255)
GREY = (128, 128, 128)
DARK_GREY = (40, 40, 40)
LIGHT_BLUE = (173, 216, 230)
TRANSPARENT = (0, 0, 0, 0)

# Boss a projektily
BOSS_COLOR = MAGENTA
BOSS_PROJECTILE_COLOR = (255, 0, 255)
MINION_COLOR = (200, 0, 0)

# Barvy pastí
TRAP_SPIKE_COLOR = (180, 180, 180)
TRAP_POISON_COLOR = (0, 180, 0)
TRAP_SLOW_COLOR = (100, 100, 255)
TRAP_EXPLOSION_COLOR = (255, 100, 0)
TRAP_TELEPORT_COLOR = (150, 0, 255)

# Typy pastí
class TrapType(Enum):
    SPIKE = 0
    POISON = 1
    SLOW = 2
    EXPLOSION = 3
    TELEPORT = 4

# Herní stavy
class GameState(Enum):
    SPLASH = 0
    MAIN_MENU = 1
    PLAYING = 2
    SHOP = 3
    GAME_OVER = 4
    SETTINGS = 5
    PAUSED = 6

# --- Hráč konstanty ---
PLAYER_SIZE = 30
PLAYER_BASE_SPEED = 120
PLAYER_START_X = SCREEN_WIDTH // 2
PLAYER_START_Y = SCREEN_HEIGHT // 2
PLAYER_MAX_HEALTH = 100

# Player Dash
PLAYER_DASH_DISTANCE = 150
PLAYER_DASH_SPEED = 800
PLAYER_DASH_COOLDOWN = 2.0
PLAYER_DASH_DURATION = 0.3
DOUBLE_TAP_TIME = 0.3

# Střelba Hráče
BULLET_SIZE = 5
BASE_BULLET_SPEED = 300
BULLET_COLOR = YELLOW
BASE_BULLET_COOLDOWN = 0.4
BASE_BULLET_DAMAGE = 2

# Světlo
INITIAL_LIGHT_RADIUS = 180.0
MAX_LIGHT_RADIUS = 450.0
MIN_LIGHT_RADIUS = 35.0
LIGHT_DEATH_THRESHOLD = -1.0
LIGHT_FLICKER_AMOUNT = 1.5
LIGHT_SHRINK_RATE = 3.0
LIGHT_GAIN_PER_KILL = 8.0
LIGHT_GROW_INTERP_FACTOR = 16.0
LIGHT_COLOR = (255, 220, 150)
AMBIENT_LIGHT = (0.03, 0.03, 0.08)

# Nepřátelé
ENEMY_BASE_SIZE = 20
ENEMY_BASE_SPEED = 80.0
ENEMY_BASE_HEALTH = 5
ENEMY_BASE_DAMAGE = 15
ENEMY_BASE_SCORE = 10
ENEMY_COLOR = RED

# Střelba nepřátel
ENEMY_SHOOTING_START_WAVE = 3
ENEMY_SHOOTING_CHANCE = 0.2
ENEMY_SHOOTING_COOLDOWN = 2.0
ENEMY_BULLET_SPEED = 150
ENEMY_BULLET_DAMAGE = 5
ENEMY_BULLET_SIZE = 8
ENEMY_BULLET_COLOR = ORANGE

# --- Boss Konstanty ---
BOSS_WAVE_INTERVAL = 5
BOSS_SIZE = 50
BOSS_HEALTH_MULTIPLIER = 8
BOSS_SCORE_HP_FACTOR = 0.1
BOSS_SPEED_MULTIPLIER = 0.8
BOSS_DAMAGE_MULTIPLIER = 2.5
BOSS_SCORE_MULTIPLIER = 10
BOSS_LIGHT_REWARD = 100

# Boss mechaniky
BOSS_ACTION_COOLDOWN_MIN = 2.5
BOSS_ACTION_COOLDOWN_MAX = 4.0
BOSS_DASH_SPEED = 450
BOSS_DASH_DURATION = 0.4
BOSS_PROJECTILE_SPEED = 280
BOSS_PROJECTILE_DAMAGE = 20
BOSS_PROJECTILE_SIZE = 10
BOSS_SUMMON_COUNT_MIN = 2
BOSS_SUMMON_COUNT_MAX = 4
BOSS_SUMMON_HEALTH_FACTOR = 0.5

# Typy bossů
class BossType(Enum):
    NORMAL = 0
    SPEED = 1
    TANK = 2
    SUMMONER = 3
    SHOOTER = 4
    SPLITTER = 5
    GRENADIER = 6

# Konstanty pro granáty
GRENADE_SIZE = 8
GRENADE_EXPLOSION_RADIUS = 80
GRENADE_EXPLOSION_DAMAGE = 30
GRENADE_FUSE_TIME = 2.5
GRENADE_SPEED = 200
GRENADE_COLOR = (255, 100, 0)
EXPLOSION_COLOR = (255, 50, 0)
EXPLOSION_DURATION = 0.3

# Wave System
WAVE_TRANSITION_DELAY = 4.0
WAVE_MESSAGE_DURATION = 2.5

# Obchod
SHOP_COLS = 3
SHOP_ITEM_WIDTH = 200
SHOP_ITEM_HEIGHT = 80
SHOP_PADDING_X = 20
SHOP_PADDING_Y = 20
SHOP_GRID_START_X = (SCREEN_WIDTH - (SHOP_COLS * SHOP_ITEM_WIDTH + (SHOP_COLS - 1) * SHOP_PADDING_X)) // 2
SHOP_GRID_START_Y = 180

# Typy zbraní
class WeaponType(Enum):
    PISTOL = 0
    SHOTGUN = 1
    MACHINEGUN = 2

# Splash Screen
SPLASH_DURATION = 5.0

# --- Nastavení okna ---
screen_flags = pygame.DOUBLEBUF
if VSYNC_ENABLED:
    screen_flags |= pygame.SCALED

try:
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT), screen_flags)
    pygame.display.set_caption(WINDOW_TITLE)
except pygame.error as e:
    print(f"Chyba okna: {e}")
    pygame.quit()
    sys.exit()

clock = pygame.time.Clock()

# --- Fonty ---
try:
    default_font = pygame.font.Font(None, 36)
    small_font = pygame.font.Font(None, 24)
    smaller_font = pygame.font.Font(None, 18)
    large_font = pygame.font.Font(None, 74)
    wave_font = pygame.font.Font(None, 50)
except Exception as e:
    print(f"Chyba fontu: {e}")
    default_font = pygame.font.SysFont(pygame.font.get_default_font(), 36)
    small_font = pygame.font.SysFont(pygame.font.get_default_font(), 24)
    smaller_font = pygame.font.SysFont(pygame.font.get_default_font(), 18)
    large_font = pygame.font.SysFont(pygame.font.get_default_font(), 74)
    wave_font = pygame.font.SysFont(pygame.font.get_default_font(), 50)

# --- High Score funkce ---
def load_highscore() -> int:
    """Načte high score ze souboru"""
    if os.path.exists(HIGH_SCORE_FILE):
        try:
            with open(HIGH_SCORE_FILE, 'r') as f:
                return int(f.read().strip())
        except (ValueError, IOError):
            print(f"Chyba čtení high score: {HIGH_SCORE_FILE}")
            return 0
    return 0

def save_highscore(score: int) -> None:
    """Uloží high score do souboru"""
    try:
        with open(HIGH_SCORE_FILE, 'w') as f:
            f.write(str(score))
    except IOError:
        print(f"Chyba zápisu high score: {HIGH_SCORE_FILE}")

# --- Pokročilý Shader Systém pro Pygame ---
class PygameShaderSystem:
    """
    Pokročilý shader systém implementovaný v pygame
    Nahrazuje OpenGL shadery pomocí surface operací a algoritmů
    """

    def __init__(self, width: int, height: int):
        self.width = width
        self.height = height
        self.game_surface = pygame.Surface((width, height), pygame.SRCALPHA)
        self.light_surface = pygame.Surface((width, height), pygame.SRCALPHA)
        self.shadow_surface = pygame.Surface((width, height), pygame.SRCALPHA)
        self.effect_surface = pygame.Surface((width, height), pygame.SRCALPHA)

        # Noise cache pro optimalizaci
        self.noise_cache = {}
        self.time_offset = 0.0

        # Předpočítané hodnoty pro optimalizaci
        self.distance_cache = {}
        self.gradient_cache = {}

    def clear_surfaces(self):
        """Vyčistí všechny surface"""
        self.game_surface.fill((0, 0, 0, 0))
        self.light_surface.fill((0, 0, 0, 0))
        self.shadow_surface.fill((0, 0, 0, 0))
        self.effect_surface.fill((0, 0, 0, 0))

    def simple_noise(self, x: float, y: float) -> float:
        """Zjednodušený noise pro lepší výkon"""
        # Velmi jednoduchý pseudonáhodný generátor
        n = int(x * 57 + y * 113 + self.time_offset * 7) & 0x7fffffff
        return (n % 1000) / 1000.0 - 0.5

    def create_light_gradient(self, center_x: int, center_y: int, radius: float,
                            color: Tuple[int, int, int], flicker_intensity: float = 1.0) -> pygame.Surface:
        """Vytvoří radiální gradient pro světlo s flicker efektem - OPTIMALIZOVÁNO"""
        # Cache key pro gradient
        cache_key = (int(radius), int(flicker_intensity * 10))
        if cache_key in self.gradient_cache:
            return self.gradient_cache[cache_key]

        gradient_surface = pygame.Surface((int(radius * 2), int(radius * 2)), pygame.SRCALPHA)

        # Jednoduchý flicker bez noise (rychlejší)
        flicker = 0.9 + 0.1 * math.sin(self.time_offset * 3)
        flicker *= flicker_intensity

        effective_radius = radius * flicker
        inner_radius = effective_radius * 0.4

        # Použij pygame.draw místo pixel-by-pixel (MNOHEM rychlejší)
        steps = 20  # Počet kruhů pro gradient
        for i in range(steps):
            step_radius = effective_radius * (1.0 - i / steps)
            intensity = 1.0 - (i / steps) ** 1.5

            alpha = int(255 * intensity * 0.3)  # Snížená intenzita pro lepší výkon
            step_color = (
                min(255, int(color[0] * intensity)),
                min(255, int(color[1] * intensity)),
                min(255, int(color[2] * intensity)),
                alpha
            )

            if step_radius > 0:
                pygame.draw.circle(gradient_surface, step_color,
                                 (int(radius), int(radius)), int(step_radius))

        # Cache gradient
        if len(self.gradient_cache) < 10:
            self.gradient_cache[cache_key] = gradient_surface

        return gradient_surface

    def apply_lighting(self, game_surface: pygame.Surface, light_pos: Tuple[int, int],
                      light_radius: float, light_color: Tuple[int, int, int],
                      ambient_color: Tuple[float, float, float]) -> pygame.Surface:
        """Aplikuje světelné efekty na herní surface - OPTIMALIZOVÁNO"""
        # DRASTICKY ZJEDNODUŠENO PRO VÝKON

        # Vytvoř tmavý overlay
        dark_surface = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
        dark_surface.fill((0, 0, 0, 180))  # Tmavé pozadí

        # Vytvoř světelný kruh (jednoduchý)
        light_surface = pygame.Surface((int(light_radius * 2), int(light_radius * 2)), pygame.SRCALPHA)

        # Jednoduchý gradient pomocí kruhů
        steps = 8  # Ještě méně kroků
        for i in range(steps):
            step_radius = light_radius * (1.0 - i / steps)
            alpha = int(255 * (1.0 - i / steps) * 0.4)

            color_with_alpha = (*light_color, alpha)
            if step_radius > 0:
                pygame.draw.circle(light_surface, color_with_alpha,
                                 (int(light_radius), int(light_radius)), int(step_radius))

        # Aplikuj světlo na tmavý overlay (vyřízni světlou oblast)
        light_rect = light_surface.get_rect(center=light_pos)
        dark_surface.blit(light_surface, light_rect, special_flags=pygame.BLEND_RGBA_SUB)

        # Aplikuj na herní surface
        result_surface = game_surface.copy()
        result_surface.blit(dark_surface, (0, 0))

        return result_surface

    def create_menu_background(self) -> pygame.Surface:
        """Vytvoří animované pozadí pro menu - OPTIMALIZOVÁNO"""
        bg_surface = pygame.Surface((self.width, self.height))

        # Základní barva
        base_color = (0, 0, 20)
        bg_surface.fill(base_color)

        # Zjednodušené animované hvězdy (méně hvězd)
        star_count = 20  # Sníženo z 50
        current_time = time.time()

        for i in range(star_count):
            # Pozice hvězdy s animací
            x = (current_time * 20 + i * 100) % self.width
            y = (current_time * 15 + i * 50) % self.height

            # Velikost a intenzita
            size = 1 + math.sin(current_time + i) * 0.5
            intensity = int(100 + 155 * math.sin(current_time * 2 + i))
            intensity = max(0, min(255, intensity))

            # Vykresli hvězdu
            star_color = (intensity, intensity, intensity)
            pygame.draw.circle(bg_surface, star_color, (int(x), int(y)), max(1, int(size)))

        # Zjednodušený gradient efekt místo složité mlhoviny
        gradient_surface = pygame.Surface((self.width, self.height), pygame.SRCALPHA)

        # Jednoduchý radiální gradient
        center_x, center_y = self.width // 2, self.height // 2
        max_radius = max(self.width, self.height) // 2

        for i in range(5):  # Jen 5 kroků místo složitého noise
            radius = max_radius * (i + 1) / 5
            alpha = int(30 * (1 - i / 5))
            color = (50 + i * 10, 30 + i * 5, 100 + i * 20, alpha)
            pygame.draw.circle(gradient_surface, color, (center_x, center_y), int(radius))

        bg_surface.blit(gradient_surface, (0, 0), special_flags=pygame.BLEND_ADD)

        return bg_surface

    def update_time(self, dt: float):
        """Aktualizuje čas pro animace"""
        self.time_offset += dt

        # Vyčisti cache občas
        if len(self.noise_cache) > 2000:
            self.noise_cache.clear()

# Globální instance shader systému
shader_system = PygameShaderSystem(SCREEN_WIDTH, SCREEN_HEIGHT)

# --- Spatial Hash Grid pro optimalizaci kolizí ---
class SpatialHashGrid:
    """Optimalizace kolizí pomocí prostorového dělení"""

    def __init__(self, cell_size: int = 50):
        self.cell_size = cell_size
        self.grid: Dict[Tuple[int, int], set] = {}

    def clear(self):
        """Vyčistí grid"""
        self.grid.clear()

    def get_cell(self, x: float, y: float) -> Tuple[int, int]:
        """Získá buňku pro danou pozici"""
        return (int(x // self.cell_size), int(y // self.cell_size))

    def insert(self, obj: Any, obj_id: int):
        """Vloží objekt do gridu"""
        if hasattr(obj, 'x') and hasattr(obj, 'y'):
            cell = self.get_cell(obj.x, obj.y)
            if cell not in self.grid:
                self.grid[cell] = set()
            self.grid[cell].add(obj_id)

    def query_nearby(self, x: float, y: float, radius: int = 1) -> set:
        """Najde objekty v okolí"""
        results = set()
        center_cell = self.get_cell(x, y)

        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                cell = (center_cell[0] + dx, center_cell[1] + dy)
                if cell in self.grid:
                    results.update(self.grid[cell])

        return results

# --- Object Pool pro optimalizaci alokace ---
class ObjectPool:
    """Pool objektů pro optimalizaci paměti"""

    def __init__(self, create_func, max_size: int = 100):
        self.pool: List[Any] = []
        self.create_func = create_func
        self.max_size = max_size

    def get(self):
        """Získá objekt z poolu"""
        if self.pool:
            return self.pool.pop()
        return self.create_func()

    def release(self, obj: Any):
        """Vrátí objekt do poolu"""
        if len(self.pool) < self.max_size:
            self.pool.append(obj)

# --- Zvukový systém ---
class SoundManager:
    """Správa zvuků a hudby"""

    def __init__(self):
        self.sounds: Dict[str, pygame.mixer.Sound] = {}
        self.music_volume = 0.5
        self.sound_volume = 0.7
        self.enabled = True

        # Vytvoř základní zvuky programově (placeholder)
        self.create_basic_sounds()

    def create_basic_sounds(self):
        """Vytvoří základní zvuky programově"""
        # Placeholder - v reálné implementaci by se načítaly ze souborů
        try:
            # Vytvoř jednoduché zvuky pomocí numpy
            sample_rate = 22050
            duration = 0.1

            # Shoot sound
            t = np.linspace(0, duration, int(sample_rate * duration))
            shoot_wave = np.sin(2 * np.pi * 800 * t) * np.exp(-t * 10)
            # Convert to stereo (2 channels)
            stereo_wave = np.column_stack((shoot_wave, shoot_wave))
            shoot_sound = pygame.sndarray.make_sound((stereo_wave * 32767).astype(np.int16))
            self.sounds['shoot'] = shoot_sound

            # Hit sound
            hit_wave = np.sin(2 * np.pi * 400 * t) * np.exp(-t * 15)
            stereo_wave = np.column_stack((hit_wave, hit_wave))
            hit_sound = pygame.sndarray.make_sound((stereo_wave * 32767).astype(np.int16))
            self.sounds['hit'] = hit_sound

            # Error sound
            error_wave = np.sin(2 * np.pi * 200 * t) * np.exp(-t * 5)
            stereo_wave = np.column_stack((error_wave, error_wave))
            error_sound = pygame.sndarray.make_sound((stereo_wave * 32767).astype(np.int16))
            self.sounds['error'] = error_sound

            # Shop buy sound
            buy_wave = np.sin(2 * np.pi * 1000 * t) * np.exp(-t * 8)
            stereo_wave = np.column_stack((buy_wave, buy_wave))
            buy_sound = pygame.sndarray.make_sound((stereo_wave * 32767).astype(np.int16))
            self.sounds['shop_buy'] = buy_sound

            # Dash sound
            dash_wave = np.sin(2 * np.pi * 600 * t) * np.exp(-t * 12)
            stereo_wave = np.column_stack((dash_wave, dash_wave))
            dash_sound = pygame.sndarray.make_sound((stereo_wave * 32767).astype(np.int16))
            self.sounds['dash'] = dash_sound

        except Exception as e:
            print(f"Chyba vytváření zvuků: {e}")
            self.enabled = False

    def play_sound(self, sound_name: str):
        """Přehraje zvuk"""
        if not self.enabled or sound_name not in self.sounds:
            return

        try:
            sound = self.sounds[sound_name]
            sound.set_volume(self.sound_volume)
            sound.play()
        except Exception as e:
            print(f"Chyba přehrávání zvuku {sound_name}: {e}")

    def set_volume(self, sound_volume: float, music_volume: float):
        """Nastaví hlasitost"""
        self.sound_volume = max(0.0, min(1.0, sound_volume))
        self.music_volume = max(0.0, min(1.0, music_volume))
        pygame.mixer.music.set_volume(self.music_volume)

# --- Herní objekty ---
@dataclass
class Vector2:
    """2D vektor"""
    x: float
    y: float

    def __add__(self, other):
        return Vector2(self.x + other.x, self.y + other.y)

    def __sub__(self, other):
        return Vector2(self.x - other.x, self.y - other.y)

    def __mul__(self, scalar: float):
        return Vector2(self.x * scalar, self.y * scalar)

    def length(self) -> float:
        return math.sqrt(self.x * self.x + self.y * self.y)

    def normalize(self):
        length = self.length()
        if length > 0:
            return Vector2(self.x / length, self.y / length)
        return Vector2(0, 0)

    def distance_to(self, other) -> float:
        return (self - other).length()

@dataclass
class GameObject:
    """Základní herní objekt"""
    position: Vector2
    velocity: Vector2
    size: float
    health: float
    max_health: float
    active: bool = True

    @property
    def x(self) -> float:
        return self.position.x

    @property
    def y(self) -> float:
        return self.position.y

    @property
    def rect(self) -> pygame.Rect:
        return pygame.Rect(
            self.x - self.size/2,
            self.y - self.size/2,
            self.size,
            self.size
        )

    def update(self, dt: float):
        """Aktualizuje objekt"""
        if self.active:
            self.position = self.position + self.velocity * dt

    def draw(self, surface: pygame.Surface, color: Tuple[int, int, int]):
        """Vykreslí objekt"""
        if self.active:
            pygame.draw.circle(surface, color, (int(self.x), int(self.y)), int(self.size/2))

class Bullet(GameObject):
    """Střela"""

    def __init__(self, position: Vector2, velocity: Vector2, damage: float, size: float = BULLET_SIZE):
        super().__init__(position, velocity, size, 1, 1)
        self.damage = damage
        self.lifetime = 3.0  # Sekund
        self.age = 0.0

    def update(self, dt: float):
        super().update(dt)
        self.age += dt

        # Deaktivuj po vypršení životnosti
        if self.age >= self.lifetime:
            self.active = False

        # Deaktivuj pokud je mimo obrazovku
        if (self.x < -50 or self.x > SCREEN_WIDTH + 50 or
            self.y < -50 or self.y > SCREEN_HEIGHT + 50):
            self.active = False

    def draw(self, surface: pygame.Surface, color: Tuple[int, int, int] = BULLET_COLOR):
        if self.active:
            pygame.draw.circle(surface, color, (int(self.x), int(self.y)), int(self.size))

# Globální systémy
spatial_grid = SpatialHashGrid(50)
sound_manager = SoundManager()

# --- Player Class ---
class Player(GameObject):
    """Hráč"""

    def __init__(self):
        position = Vector2(PLAYER_START_X, PLAYER_START_Y)
        velocity = Vector2(0, 0)
        super().__init__(position, velocity, PLAYER_SIZE, PLAYER_MAX_HEALTH, PLAYER_MAX_HEALTH)

        # Herní statistiky
        self.score = 0
        self.light_radius = INITIAL_LIGHT_RADIUS
        self.light_to_add = 0.0

        # Upgrady
        self.speed_multiplier = 1.0
        self.fire_rate_multiplier = 1.0
        self.bullet_damage = BASE_BULLET_DAMAGE
        self.armor = 0

        # Dash systém
        self.dash_available = False
        self.dash_distance = PLAYER_DASH_DISTANCE
        self.dash_cooldown = PLAYER_DASH_COOLDOWN
        self.dash_speed = PLAYER_DASH_SPEED
        self.dash_duration = PLAYER_DASH_DURATION
        self.is_dashing = False
        self.dash_timer = 0.0
        self.dash_cooldown_timer = 0.0

        # Střelba
        self.last_shot_time = 0.0
        self.current_weapon = WeaponType.PISTOL

        # Efekty
        self.poison_timer = 0.0
        self.slow_timer = 0.0
        self.slow_factor = 1.0

        # Double-tap detekce
        self.last_space_press = 0.0
        self.space_press_count = 0

    def update(self, dt: float, keys_pressed: Dict[int, bool]):
        """Aktualizuje hráče"""
        if not self.active:
            return

        # Aktualizuj timery
        self.dash_cooldown_timer = max(0, self.dash_cooldown_timer - dt)
        self.poison_timer = max(0, self.poison_timer - dt)
        self.slow_timer = max(0, self.slow_timer - dt)

        # Resetuj slow efekt
        if self.slow_timer <= 0:
            self.slow_factor = 1.0

        # Poison damage
        if self.poison_timer > 0:
            self.take_damage(TRAP_POISON_DAMAGE * dt)

        # Dash logika
        if self.is_dashing:
            self.dash_timer -= dt
            if self.dash_timer <= 0:
                self.is_dashing = False
                self.velocity = Vector2(0, 0)
        else:
            # Normální pohyb
            self.handle_movement(dt, keys_pressed)

        # Aktualizuj pozici
        super().update(dt)

        # Omeź na obrazovku
        self.position.x = max(self.size/2, min(SCREEN_WIDTH - self.size/2, self.position.x))
        self.position.y = max(self.size/2, min(SCREEN_HEIGHT - self.size/2, self.position.y))

        # Aktualizuj světlo
        self.update_light(dt)

    def handle_movement(self, dt: float, keys_pressed: Dict[int, bool]):
        """Zpracuje pohyb hráče"""
        move_x, move_y = 0, 0

        if keys_pressed.get(pygame.K_a, False) or keys_pressed.get(pygame.K_LEFT, False):
            move_x -= 1
        if keys_pressed.get(pygame.K_d, False) or keys_pressed.get(pygame.K_RIGHT, False):
            move_x += 1
        if keys_pressed.get(pygame.K_w, False) or keys_pressed.get(pygame.K_UP, False):
            move_y -= 1
        if keys_pressed.get(pygame.K_s, False) or keys_pressed.get(pygame.K_DOWN, False):
            move_y += 1

        # Normalizuj diagonální pohyb
        if move_x != 0 and move_y != 0:
            move_x *= 0.707  # 1/sqrt(2)
            move_y *= 0.707

        # Aplikuj rychlost a efekty
        speed = PLAYER_BASE_SPEED * self.speed_multiplier * self.slow_factor
        self.velocity = Vector2(move_x * speed, move_y * speed)

    def try_dash(self):
        """Pokusí se o dash"""
        if (self.dash_available and not self.is_dashing and
            self.dash_cooldown_timer <= 0 and self.velocity.length() > 0):

            # Spusť dash
            self.is_dashing = True
            self.dash_timer = self.dash_duration
            self.dash_cooldown_timer = self.dash_cooldown

            # Nastav rychlost dashe
            direction = self.velocity.normalize()
            self.velocity = direction * self.dash_speed

            sound_manager.play_sound('dash')
            return True
        return False

    def shoot(self, target_pos: Vector2) -> Optional[Bullet]:
        """Vystřelí střelu směrem k cíli"""
        current_time = time.time()
        cooldown = BASE_BULLET_COOLDOWN / self.fire_rate_multiplier

        if current_time - self.last_shot_time >= cooldown:
            self.last_shot_time = current_time

            # Směr střelby
            direction = (target_pos - self.position).normalize()
            bullet_velocity = direction * BASE_BULLET_SPEED

            # Vytvoř střelu
            bullet = Bullet(
                Vector2(self.position.x, self.position.y),
                bullet_velocity,
                self.bullet_damage
            )

            sound_manager.play_sound('shoot')
            return bullet

        return None

    def take_damage(self, damage: float) -> bool:
        """Způsobí poškození hráči"""
        # Aplikuj armor
        actual_damage = max(1, damage - self.armor)
        self.health -= actual_damage

        if self.health <= 0:
            self.health = 0
            self.active = False
            return True  # Hráč zemřel

        sound_manager.play_sound('hit')
        return False

    def add_score(self, points: int):
        """Přidá skóre"""
        self.score += points

    def update_light(self, dt: float):
        """Aktualizuje světelný systém"""
        # Přidej světlo z upgradů
        if self.light_to_add > 0:
            add_amount = min(self.light_to_add, LIGHT_GROW_INTERP_FACTOR * dt)
            self.light_radius += add_amount
            self.light_to_add -= add_amount

        # Omeź světlo
        self.light_radius = max(MIN_LIGHT_RADIUS, min(MAX_LIGHT_RADIUS, self.light_radius))

        # Zmenšování světla v čase
        self.light_radius -= LIGHT_SHRINK_RATE * dt

        # Kontrola smrti světlem
        if self.light_radius <= LIGHT_DEATH_THRESHOLD:
            self.active = False

    def add_light(self, amount: float):
        """Přidá světlo"""
        self.light_to_add += amount

    def apply_poison(self, duration: float):
        """Aplikuje poison efekt"""
        self.poison_timer = max(self.poison_timer, duration)

    def apply_slow(self, factor: float, duration: float):
        """Aplikuje slow efekt"""
        self.slow_factor = min(self.slow_factor, factor)
        self.slow_timer = max(self.slow_timer, duration)

    def draw(self, surface: pygame.Surface):
        """Vykreslí hráče"""
        if not self.active:
            return

        # Základní kruh hráče
        color = WHITE
        if self.is_dashing:
            color = CYAN
        elif self.poison_timer > 0:
            color = GREEN
        elif self.slow_timer > 0:
            color = BLUE

        pygame.draw.circle(surface, color, (int(self.x), int(self.y)), int(self.size/2))

        # Health bar
        if self.health < self.max_health:
            bar_width = 40
            bar_height = 6
            bar_x = int(self.x - bar_width/2)
            bar_y = int(self.y - self.size/2 - 10)

            # Pozadí
            pygame.draw.rect(surface, RED, (bar_x, bar_y, bar_width, bar_height))

            # Zdraví
            health_width = int(bar_width * (self.health / self.max_health))
            pygame.draw.rect(surface, GREEN, (bar_x, bar_y, health_width, bar_height))

# --- Enemy Class ---
class Enemy(GameObject):
    """Nepřítel"""

    def __init__(self, position: Vector2, wave: int):
        # Škálování podle vlny
        health = ENEMY_BASE_HEALTH + wave * 2
        size = ENEMY_BASE_SIZE + wave * 0.5

        super().__init__(position, Vector2(0, 0), size, health, health)

        self.wave = wave
        self.speed = ENEMY_BASE_SPEED + wave * 5
        self.damage = ENEMY_BASE_DAMAGE + wave * 2
        self.score_value = ENEMY_BASE_SCORE + wave * 5

        # Střelba
        self.can_shoot = (wave >= ENEMY_SHOOTING_START_WAVE and
                         random.random() < ENEMY_SHOOTING_CHANCE)
        self.last_shot_time = 0.0
        self.shoot_cooldown = ENEMY_SHOOTING_COOLDOWN + random.uniform(-0.5, 0.5)

        # AI
        self.target_position = Vector2(0, 0)
        self.state = "chase"  # chase, attack, flee
        self.state_timer = 0.0

    def update(self, dt: float, player: Player) -> List[Bullet]:
        """Aktualizuje nepřítele"""
        if not self.active:
            return []

        bullets_fired = []
        self.state_timer += dt

        # AI logika
        distance_to_player = self.position.distance_to(player.position)

        if distance_to_player > 300:
            self.state = "chase"
        elif distance_to_player < 50:
            self.state = "attack"
        else:
            self.state = "chase"

        # Pohyb podle stavu
        if self.state == "chase":
            direction = (player.position - self.position).normalize()
            self.velocity = direction * self.speed
        elif self.state == "attack":
            # Krouží kolem hráče
            angle = math.atan2(player.position.y - self.position.y,
                             player.position.x - self.position.x)
            angle += math.pi / 2  # Perpendicular

            direction = Vector2(math.cos(angle), math.sin(angle))
            self.velocity = direction * (self.speed * 0.7)

        # Aktualizuj pozici
        super().update(dt)

        # Omeź na obrazovku
        self.position.x = max(0, min(SCREEN_WIDTH, self.position.x))
        self.position.y = max(0, min(SCREEN_HEIGHT, self.position.y))

        # Střelba
        if self.can_shoot and distance_to_player < 200:
            current_time = time.time()
            if current_time - self.last_shot_time >= self.shoot_cooldown:
                self.last_shot_time = current_time

                # Vystřel na hráče
                direction = (player.position - self.position).normalize()
                bullet_velocity = direction * ENEMY_BULLET_SPEED

                bullet = Bullet(
                    Vector2(self.position.x, self.position.y),
                    bullet_velocity,
                    ENEMY_BULLET_DAMAGE,
                    ENEMY_BULLET_SIZE
                )
                bullets_fired.append(bullet)

        return bullets_fired

    def take_damage(self, damage: float) -> bool:
        """Způsobí poškození nepříteli"""
        self.health -= damage

        if self.health <= 0:
            self.active = False
            return True  # Nepřítel zemřel

        return False

    def draw(self, surface: pygame.Surface):
        """Vykreslí nepřítele"""
        if not self.active:
            return

        # Základní kruh
        color = ENEMY_COLOR
        if self.can_shoot:
            color = ORANGE  # Střelci jsou oranžoví

        pygame.draw.circle(surface, color, (int(self.x), int(self.y)), int(self.size/2))

        # Health bar pro silnější nepřátele
        if self.max_health > ENEMY_BASE_HEALTH:
            bar_width = int(self.size)
            bar_height = 4
            bar_x = int(self.x - bar_width/2)
            bar_y = int(self.y - self.size/2 - 8)

            # Pozadí
            pygame.draw.rect(surface, RED, (bar_x, bar_y, bar_width, bar_height))

            # Zdraví
            health_width = int(bar_width * (self.health / self.max_health))
            pygame.draw.rect(surface, GREEN, (bar_x, bar_y, health_width, bar_height))

# --- Trap Class ---
class Trap:
    """Past"""

    def __init__(self, position: Vector2, trap_type: TrapType):
        self.position = position
        self.type = trap_type
        self.triggered = False
        self.active = True

        # Nastavení podle typu
        if trap_type == TrapType.SPIKE:
            self.size = TRAP_SPIKE_SIZE
            self.damage = TRAP_SPIKE_DAMAGE
            self.color = TRAP_SPIKE_COLOR
        elif trap_type == TrapType.POISON:
            self.size = TRAP_POISON_SIZE
            self.damage = TRAP_POISON_DAMAGE
            self.duration = TRAP_POISON_DURATION
            self.color = TRAP_POISON_COLOR
        elif trap_type == TrapType.SLOW:
            self.size = TRAP_SLOW_SIZE
            self.slow_factor = TRAP_SLOW_FACTOR
            self.duration = TRAP_SLOW_DURATION
            self.color = TRAP_SLOW_COLOR
        elif trap_type == TrapType.EXPLOSION:
            self.size = TRAP_EXPLOSION_SIZE
            self.damage = TRAP_EXPLOSION_DAMAGE
            self.color = TRAP_EXPLOSION_COLOR
        elif trap_type == TrapType.TELEPORT:
            self.size = TRAP_TELEPORT_SIZE
            self.color = TRAP_TELEPORT_COLOR

    @property
    def x(self) -> float:
        return self.position.x

    @property
    def y(self) -> float:
        return self.position.y

    @property
    def rect(self) -> pygame.Rect:
        return pygame.Rect(
            self.x - self.size/2,
            self.y - self.size/2,
            self.size,
            self.size
        )

    def check_collision(self, player: Player) -> bool:
        """Kontroluje kolizi s hráčem"""
        if not self.active or self.triggered:
            return False

        distance = self.position.distance_to(player.position)
        return distance < (self.size/2 + player.size/2)

    def trigger(self, player: Player):
        """Aktivuje past"""
        if self.triggered:
            return

        self.triggered = True

        if self.type == TrapType.SPIKE:
            player.take_damage(self.damage)
        elif self.type == TrapType.POISON:
            player.apply_poison(self.duration)
        elif self.type == TrapType.SLOW:
            player.apply_slow(self.slow_factor, self.duration)
        elif self.type == TrapType.EXPLOSION:
            player.take_damage(self.damage)
            # TODO: Přidat explosion efekt
        elif self.type == TrapType.TELEPORT:
            # Teleportuj hráče na náhodné místo
            new_x = random.randint(50, SCREEN_WIDTH - 50)
            new_y = random.randint(50, SCREEN_HEIGHT - 50)
            player.position = Vector2(new_x, new_y)

        sound_manager.play_sound('hit')

    def draw(self, surface: pygame.Surface):
        """Vykreslí past"""
        if not self.active:
            return

        # Základní tvar podle typu
        if self.type == TrapType.SPIKE:
            # Trojúhelník pro bodce
            points = [
                (int(self.x), int(self.y - self.size/2)),
                (int(self.x - self.size/2), int(self.y + self.size/2)),
                (int(self.x + self.size/2), int(self.y + self.size/2))
            ]
            pygame.draw.polygon(surface, self.color, points)
        else:
            # Kruh pro ostatní
            pygame.draw.circle(surface, self.color, (int(self.x), int(self.y)), int(self.size/2))

        # Indikátor triggeru
        if self.triggered:
            pygame.draw.circle(surface, WHITE, (int(self.x), int(self.y)), int(self.size/2), 2)

# --- Game Manager ---
class GameManager:
    """Hlavní správce hry"""

    def __init__(self):
        self.state = GameState.SPLASH
        self.player = Player()
        self.enemies: List[Enemy] = []
        self.bullets: List[Bullet] = []
        self.enemy_bullets: List[Bullet] = []
        self.traps: List[Trap] = []

        # Wave systém
        self.current_wave = 0
        self.enemies_to_spawn = 0
        self.enemies_alive = 0
        self.wave_transition_timer = 0.0
        self.wave_message_timer = 0.0
        self.wave_message_text = ""

        # UI
        self.high_score = load_highscore()
        self.death_reason = ""

        # Timery
        self.splash_start_time = time.time()
        self.pause_time = 0.0

        # Object pools
        self.bullet_pool = ObjectPool(self.create_bullet, 200)
        self.enemy_bullet_pool = ObjectPool(self.create_enemy_bullet, 100)

        # Input
        self.keys_pressed = {}
        self.mouse_pos = (0, 0)
        self.mouse_buttons = (False, False, False)

    def create_bullet(self) -> Bullet:
        """Vytvoří novou střelu pro pool"""
        return Bullet(Vector2(0, 0), Vector2(0, 0), 0)

    def create_enemy_bullet(self) -> Bullet:
        """Vytvoří novou nepřátelskou střelu pro pool"""
        return Bullet(Vector2(0, 0), Vector2(0, 0), 0, ENEMY_BULLET_SIZE)

    def update(self, dt: float):
        """Hlavní update loop"""
        shader_system.update_time(dt)

        if self.state == GameState.SPLASH:
            self.update_splash(dt)
        elif self.state == GameState.MAIN_MENU:
            self.update_main_menu(dt)
        elif self.state == GameState.PLAYING:
            self.update_playing(dt)
        elif self.state == GameState.SHOP:
            self.update_shop(dt)
        elif self.state == GameState.GAME_OVER:
            self.update_game_over(dt)
        elif self.state == GameState.PAUSED:
            self.update_paused(dt)

    def update_splash(self, dt: float):
        """Update splash screen"""
        if time.time() - self.splash_start_time > SPLASH_DURATION:
            self.state = GameState.MAIN_MENU

    def update_main_menu(self, dt: float):
        """Update main menu"""
        pass  # Handled by input events

    def update_playing(self, dt: float):
        """Update gameplay"""
        if not self.player.active:
            self.end_game("Player died")
            return

        # Update player
        self.player.update(dt, self.keys_pressed)

        # Player shooting
        if self.mouse_buttons[0]:  # Left click
            bullet = self.player.shoot(Vector2(self.mouse_pos[0], self.mouse_pos[1]))
            if bullet:
                self.bullets.append(bullet)

        # Update enemies
        for enemy in self.enemies[:]:
            if enemy.active:
                enemy_bullets = enemy.update(dt, self.player)
                self.enemy_bullets.extend(enemy_bullets)
            else:
                self.enemies.remove(enemy)
                self.enemies_alive -= 1
                self.player.add_score(enemy.score_value)
                self.player.add_light(LIGHT_GAIN_PER_KILL)

        # Update bullets
        self.update_bullets(dt)

        # Update traps
        for trap in self.traps:
            if trap.check_collision(self.player):
                trap.trigger(self.player)

        # Wave management
        self.update_wave_system(dt)

        # Collision detection
        self.check_collisions()

    def update_bullets(self, dt: float):
        """Aktualizuje střely"""
        # Player bullets
        for bullet in self.bullets[:]:
            bullet.update(dt)
            if not bullet.active:
                self.bullets.remove(bullet)
                self.bullet_pool.release(bullet)

        # Enemy bullets
        for bullet in self.enemy_bullets[:]:
            bullet.update(dt)
            if not bullet.active:
                self.enemy_bullets.remove(bullet)
                self.enemy_bullet_pool.release(bullet)

    def update_wave_system(self, dt: float):
        """Aktualizuje wave systém"""
        # Wave message timer
        if self.wave_message_timer > 0:
            self.wave_message_timer -= dt

        # Check if wave is complete
        if self.enemies_alive == 0 and self.enemies_to_spawn == 0:
            if self.wave_transition_timer <= 0:
                self.start_next_wave()
            else:
                self.wave_transition_timer -= dt

        # Spawn enemies
        if self.enemies_to_spawn > 0 and len(self.enemies) < 20:  # Limit concurrent enemies
            self.spawn_enemy()
            self.enemies_to_spawn -= 1

    def start_next_wave(self):
        """Spustí další vlnu"""
        self.current_wave += 1
        self.enemies_to_spawn = 3 + self.current_wave * 2  # Více nepřátel každou vlnu
        self.enemies_alive = self.enemies_to_spawn
        self.wave_transition_timer = WAVE_TRANSITION_DELAY

        # Wave message
        self.wave_message_text = f"Wave {self.current_wave}"
        self.wave_message_timer = WAVE_MESSAGE_DURATION

        # Spawn traps occasionally
        if random.random() < TRAP_SPAWN_CHANCE:
            self.spawn_trap()

    def spawn_enemy(self):
        """Vytvoří nového nepřítele"""
        # Random spawn position at edge of screen
        side = random.randint(0, 3)
        if side == 0:  # Top
            pos = Vector2(random.randint(0, SCREEN_WIDTH), -20)
        elif side == 1:  # Right
            pos = Vector2(SCREEN_WIDTH + 20, random.randint(0, SCREEN_HEIGHT))
        elif side == 2:  # Bottom
            pos = Vector2(random.randint(0, SCREEN_WIDTH), SCREEN_HEIGHT + 20)
        else:  # Left
            pos = Vector2(-20, random.randint(0, SCREEN_HEIGHT))

        enemy = Enemy(pos, self.current_wave)
        self.enemies.append(enemy)

    def spawn_trap(self):
        """Vytvoří novou past"""
        # Random position not too close to player
        attempts = 10
        for _ in range(attempts):
            x = random.randint(50, SCREEN_WIDTH - 50)
            y = random.randint(50, SCREEN_HEIGHT - 50)
            pos = Vector2(x, y)

            if pos.distance_to(self.player.position) > 100:
                trap_type = random.choice(list(TrapType))
                trap = Trap(pos, trap_type)
                self.traps.append(trap)
                break

    def check_collisions(self):
        """Kontroluje kolize - OPTIMALIZOVÁNO"""
        player_rect = self.player.rect

        # Player bullets vs enemies - optimalizováno
        bullets_to_remove = []
        enemies_to_remove = []

        for i, bullet in enumerate(self.bullets):
            if not bullet.active:
                continue
            bullet_rect = bullet.rect

            for j, enemy in enumerate(self.enemies):
                if not enemy.active:
                    continue

                # Rychlá distance check před rect collision
                dx = bullet.x - enemy.x
                dy = bullet.y - enemy.y
                if dx*dx + dy*dy < (bullet.size + enemy.size)**2:
                    if enemy.take_damage(bullet.damage):
                        enemies_to_remove.append(j)
                    bullets_to_remove.append(i)
                    break

        # Odstraň neaktivní objekty
        for i in reversed(bullets_to_remove):
            if i < len(self.bullets):
                self.bullets[i].active = False

        # Enemy bullets vs player - zjednodušeno
        for bullet in self.enemy_bullets:
            if bullet.active and self.player.active:
                dx = bullet.x - self.player.x
                dy = bullet.y - self.player.y
                if dx*dx + dy*dy < (bullet.size + self.player.size)**2:
                    self.player.take_damage(bullet.damage)
                    bullet.active = False

        # Enemies vs player - zjednodušeno
        for enemy in self.enemies:
            if enemy.active and self.player.active:
                dx = enemy.x - self.player.x
                dy = enemy.y - self.player.y
                if dx*dx + dy*dy < (enemy.size + self.player.size)**2:
                    self.player.take_damage(enemy.damage)
                    enemy.take_damage(999)  # Kill enemy on contact

    def update_shop(self, dt: float):
        """Update shop"""
        pass  # Handled by input events

    def update_game_over(self, dt: float):
        """Update game over screen"""
        pass  # Handled by input events

    def update_paused(self, dt: float):
        """Update paused state"""
        pass  # Handled by input events

    def start_new_game(self):
        """Spustí novou hru"""
        self.player = Player()
        self.enemies.clear()
        self.bullets.clear()
        self.enemy_bullets.clear()
        self.traps.clear()
        self.current_wave = 0
        self.enemies_to_spawn = 0
        self.enemies_alive = 0
        self.wave_transition_timer = 0.0
        self.wave_message_timer = 0.0
        self.wave_message_text = ""
        self.death_reason = ""
        self.state = GameState.PLAYING
        self.start_next_wave()

    def end_game(self, reason: str):
        """Ukončí hru"""
        self.death_reason = reason
        if self.player.score > self.high_score:
            self.high_score = self.player.score
            save_highscore(self.high_score)
        self.state = GameState.GAME_OVER

    def draw(self, surface: pygame.Surface):
        """Hlavní vykreslování"""
        if self.state == GameState.SPLASH:
            self.draw_splash(surface)
        elif self.state == GameState.MAIN_MENU:
            self.draw_main_menu(surface)
        elif self.state == GameState.PLAYING:
            self.draw_playing(surface)
        elif self.state == GameState.SHOP:
            self.draw_shop(surface)
        elif self.state == GameState.GAME_OVER:
            self.draw_game_over(surface)
        elif self.state == GameState.PAUSED:
            self.draw_paused(surface)

    def draw_splash(self, surface: pygame.Surface):
        """Vykreslí splash screen"""
        surface.fill(BLACK)

        # Logo
        title_text = large_font.render("LIGHT OR DEAD", True, WHITE)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 50))
        surface.blit(title_text, title_rect)

        # Subtitle
        subtitle_text = default_font.render("Pygame Community Edition", True, GREY)
        subtitle_rect = subtitle_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 20))
        surface.blit(subtitle_text, subtitle_rect)

        # Progress
        progress = min(1.0, (time.time() - self.splash_start_time) / SPLASH_DURATION)
        bar_width = 300
        bar_height = 10
        bar_x = (SCREEN_WIDTH - bar_width) // 2
        bar_y = SCREEN_HEIGHT - 100

        pygame.draw.rect(surface, GREY, (bar_x, bar_y, bar_width, bar_height))
        pygame.draw.rect(surface, WHITE, (bar_x, bar_y, int(bar_width * progress), bar_height))

    def draw_main_menu(self, surface: pygame.Surface):
        """Vykreslí hlavní menu"""
        # Animované pozadí
        bg = shader_system.create_menu_background()
        surface.blit(bg, (0, 0))

        # Semi-transparent overlay
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 150))
        surface.blit(overlay, (0, 0))

        # Title
        title_text = large_font.render("LIGHT OR DEAD", True, WHITE)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH//2, 120))
        surface.blit(title_text, title_rect)

        # Buttons
        button_width = 300
        button_height = 60
        button_spacing = 20
        start_y = SCREEN_HEIGHT//2 - 50

        # Start Game
        start_rect = pygame.Rect((SCREEN_WIDTH - button_width)//2, start_y, button_width, button_height)
        self.draw_button(surface, "Start Game", start_rect, GREEN, DARK_GREY)

        # Settings
        settings_rect = pygame.Rect((SCREEN_WIDTH - button_width)//2,
                                  start_y + button_height + button_spacing,
                                  button_width, button_height)
        self.draw_button(surface, "Settings", settings_rect, BLUE, DARK_GREY)

        # Quit
        quit_rect = pygame.Rect((SCREEN_WIDTH - button_width)//2,
                              start_y + (button_height + button_spacing) * 2,
                              button_width, button_height)
        self.draw_button(surface, "Quit", quit_rect, RED, DARK_GREY)

        # High Score
        score_text = default_font.render(f"High Score: {self.high_score}", True, WHITE)
        score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT - 50))
        surface.blit(score_text, score_rect)

    def draw_playing(self, surface: pygame.Surface):
        """Vykreslí herní obrazovku - OPTIMALIZOVÁNO"""
        # Vykresli přímo na hlavní surface (bez extra kopírování)
        surface.fill(BLACK)

        # Traps
        for trap in self.traps:
            trap.draw(surface)

        # Enemies
        for enemy in self.enemies:
            enemy.draw(surface)

        # Bullets
        for bullet in self.bullets:
            bullet.draw(surface, BULLET_COLOR)

        for bullet in self.enemy_bullets:
            bullet.draw(surface, ENEMY_BULLET_COLOR)

        # Player
        self.player.draw(surface)

        # Zjednodušené světelné efekty - jen tmavý overlay s výřezem
        if self.player.light_radius > 0:
            # Tmavý overlay
            dark_overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
            dark_overlay.fill((0, 0, 0, 150))

            # Světelný kruh (jednoduchý)
            light_radius = int(self.player.light_radius)
            pygame.draw.circle(dark_overlay, (0, 0, 0, 0),
                             (int(self.player.x), int(self.player.y)), light_radius)

            # Aplikuj overlay
            surface.blit(dark_overlay, (0, 0))

        # UI overlay
        self.draw_ui(surface)

    def draw_ui(self, surface: pygame.Surface):
        """Vykreslí UI"""
        # Health bar
        bar_width = 200
        bar_height = 20
        bar_x = 20
        bar_y = 20

        pygame.draw.rect(surface, RED, (bar_x, bar_y, bar_width, bar_height))
        health_width = int(bar_width * (self.player.health / self.player.max_health))
        pygame.draw.rect(surface, GREEN, (bar_x, bar_y, health_width, bar_height))

        health_text = small_font.render(f"Health: {int(self.player.health)}/{int(self.player.max_health)}",
                                      True, WHITE)
        surface.blit(health_text, (bar_x, bar_y + bar_height + 5))

        # Light radius bar
        light_bar_y = bar_y + 60
        pygame.draw.rect(surface, DARK_GREY, (bar_x, light_bar_y, bar_width, bar_height))
        light_width = int(bar_width * (self.player.light_radius / MAX_LIGHT_RADIUS))
        pygame.draw.rect(surface, YELLOW, (bar_x, light_bar_y, light_width, bar_height))

        light_text = small_font.render(f"Light: {int(self.player.light_radius)}", True, WHITE)
        surface.blit(light_text, (bar_x, light_bar_y + bar_height + 5))

        # Score
        score_text = default_font.render(f"Score: {self.player.score}", True, WHITE)
        surface.blit(score_text, (SCREEN_WIDTH - 200, 20))

        # Wave
        wave_text = default_font.render(f"Wave: {self.current_wave}", True, WHITE)
        surface.blit(wave_text, (SCREEN_WIDTH - 200, 60))

        # Wave message
        if self.wave_message_timer > 0:
            wave_msg = wave_font.render(self.wave_message_text, True, WHITE)
            wave_rect = wave_msg.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 100))
            surface.blit(wave_msg, wave_rect)

        # Dash cooldown
        if self.player.dash_available and self.player.dash_cooldown_timer > 0:
            cooldown_text = small_font.render(f"Dash: {self.player.dash_cooldown_timer:.1f}s",
                                            True, CYAN)
            surface.blit(cooldown_text, (20, 140))

    def draw_button(self, surface: pygame.Surface, text: str, rect: pygame.Rect,
                   color: Tuple[int, int, int], border_color: Tuple[int, int, int]):
        """Vykreslí tlačítko"""
        mouse_pos = pygame.mouse.get_pos()
        is_hover = rect.collidepoint(mouse_pos)

        # Background
        button_color = tuple(min(255, c + 30) for c in color) if is_hover else color
        pygame.draw.rect(surface, button_color, rect, border_radius=10)
        pygame.draw.rect(surface, border_color, rect, 3, border_radius=10)

        # Text
        text_surface = default_font.render(text, True, WHITE)
        text_rect = text_surface.get_rect(center=rect.center)
        surface.blit(text_surface, text_rect)

        return is_hover

    def draw_shop(self, surface: pygame.Surface):
        """Vykreslí obchod"""
        # Semi-transparent background
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 200))
        surface.blit(overlay, (0, 0))

        # Title
        title_text = large_font.render("SHOP", True, WHITE)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH//2, 60))
        surface.blit(title_text, title_rect)

        # Player stats
        stats_text = default_font.render(f"Score: {self.player.score}", True, YELLOW)
        surface.blit(stats_text, (SCREEN_WIDTH - 200, 20))

        # Shop items (simplified)
        items = [
            ("Health Pack", 50, "+40 HP"),
            ("Fire Rate", 150, "+15% Speed"),
            ("Bullet Damage", 120, "+1 Damage"),
            ("Light Radius", 75, "+50 Radius"),
            ("Move Speed", 200, "+15% Speed"),
            ("Dash Unlock", 250, "Double-tap SPACE")
        ]

        item_width = 250
        item_height = 80
        items_per_row = 2
        start_x = (SCREEN_WIDTH - items_per_row * item_width - 50) // 2
        start_y = 150

        for i, (name, cost, desc) in enumerate(items):
            row = i // items_per_row
            col = i % items_per_row
            x = start_x + col * (item_width + 50)
            y = start_y + row * (item_height + 30)

            item_rect = pygame.Rect(x, y, item_width, item_height)

            # Can afford?
            can_afford = self.player.score >= cost
            bg_color = DARK_GREY if can_afford else (60, 60, 60)
            text_color = WHITE if can_afford else GREY

            pygame.draw.rect(surface, bg_color, item_rect, border_radius=10)
            pygame.draw.rect(surface, WHITE, item_rect, 2, border_radius=10)

            # Text
            name_text = small_font.render(name, True, text_color)
            name_rect = name_text.get_rect(center=(x + item_width//2, y + 20))
            surface.blit(name_text, name_rect)

            desc_text = smaller_font.render(desc, True, text_color)
            desc_rect = desc_text.get_rect(center=(x + item_width//2, y + 40))
            surface.blit(desc_text, desc_rect)

            cost_text = small_font.render(f"{cost} pts", True, YELLOW if can_afford else GREY)
            cost_rect = cost_text.get_rect(center=(x + item_width//2, y + 60))
            surface.blit(cost_text, cost_rect)

        # Exit button
        exit_rect = pygame.Rect(SCREEN_WIDTH//2 - 100, SCREEN_HEIGHT - 80, 200, 50)
        self.draw_button(surface, "Exit Shop", exit_rect, RED, WHITE)

    def draw_game_over(self, surface: pygame.Surface):
        """Vykreslí game over screen"""
        surface.fill(BLACK)

        # Game Over text
        game_over_text = large_font.render("GAME OVER", True, RED)
        game_over_rect = game_over_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 100))
        surface.blit(game_over_text, game_over_rect)

        # Death reason
        if self.death_reason:
            reason_text = default_font.render(self.death_reason, True, WHITE)
            reason_rect = reason_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 50))
            surface.blit(reason_text, reason_rect)

        # Final score
        score_text = default_font.render(f"Final Score: {self.player.score}", True, WHITE)
        score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        surface.blit(score_text, score_rect)

        # High score
        if self.player.score == self.high_score:
            new_high_text = default_font.render("NEW HIGH SCORE!", True, YELLOW)
            new_high_rect = new_high_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 40))
            surface.blit(new_high_text, new_high_rect)
        else:
            high_text = default_font.render(f"High Score: {self.high_score}", True, GREY)
            high_rect = high_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 40))
            surface.blit(high_text, high_rect)

        # Buttons
        button_width = 200
        button_height = 50
        button_y = SCREEN_HEIGHT//2 + 100

        restart_rect = pygame.Rect(SCREEN_WIDTH//2 - button_width - 20, button_y, button_width, button_height)
        self.draw_button(surface, "Play Again", restart_rect, GREEN, WHITE)

        menu_rect = pygame.Rect(SCREEN_WIDTH//2 + 20, button_y, button_width, button_height)
        self.draw_button(surface, "Main Menu", menu_rect, BLUE, WHITE)

    def draw_paused(self, surface: pygame.Surface):
        """Vykreslí pauzu"""
        # Draw game in background (dimmed)
        self.draw_playing(surface)

        # Overlay
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 150))
        surface.blit(overlay, (0, 0))

        # Paused text
        paused_text = large_font.render("PAUSED", True, WHITE)
        paused_rect = paused_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        surface.blit(paused_text, paused_rect)

        # Instructions
        instruction_text = default_font.render("Press ESC to resume", True, GREY)
        instruction_rect = instruction_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 50))
        surface.blit(instruction_text, instruction_rect)

    def handle_input(self, event: pygame.event.Event):
        """Zpracuje input eventy"""
        if event.type == pygame.KEYDOWN:
            self.keys_pressed[event.key] = True

            # Global keys
            if event.key == pygame.K_ESCAPE:
                if self.state == GameState.PLAYING:
                    self.state = GameState.PAUSED
                elif self.state == GameState.PAUSED:
                    self.state = GameState.PLAYING
                elif self.state in [GameState.SHOP, GameState.SETTINGS]:
                    self.state = GameState.MAIN_MENU

            # State-specific keys
            if self.state == GameState.SPLASH:
                self.state = GameState.MAIN_MENU
            elif self.state == GameState.PLAYING:
                if event.key == pygame.K_SPACE:
                    # Double-tap dash detection
                    current_time = time.time()
                    if current_time - self.player.last_space_press < DOUBLE_TAP_TIME:
                        self.player.space_press_count += 1
                        if self.player.space_press_count >= 2:
                            self.player.try_dash()
                            self.player.space_press_count = 0
                    else:
                        self.player.space_press_count = 1
                    self.player.last_space_press = current_time
                elif event.key == pygame.K_p:
                    self.state = GameState.SHOP

        elif event.type == pygame.KEYUP:
            self.keys_pressed[event.key] = False

        elif event.type == pygame.MOUSEBUTTONDOWN:
            self.mouse_buttons = pygame.mouse.get_pressed()
            mouse_pos = pygame.mouse.get_pos()

            if self.state == GameState.MAIN_MENU:
                # Check button clicks (simplified)
                if SCREEN_HEIGHT//2 - 50 <= mouse_pos[1] <= SCREEN_HEIGHT//2 - 50 + 60:
                    if (SCREEN_WIDTH - 300)//2 <= mouse_pos[0] <= (SCREEN_WIDTH + 300)//2:
                        self.start_new_game()
                elif SCREEN_HEIGHT//2 + 10 <= mouse_pos[1] <= SCREEN_HEIGHT//2 + 70:
                    if (SCREEN_WIDTH - 300)//2 <= mouse_pos[0] <= (SCREEN_WIDTH + 300)//2:
                        self.state = GameState.SETTINGS
                elif SCREEN_HEIGHT//2 + 80 <= mouse_pos[1] <= SCREEN_HEIGHT//2 + 140:
                    if (SCREEN_WIDTH - 300)//2 <= mouse_pos[0] <= (SCREEN_WIDTH + 300)//2:
                        pygame.quit()
                        sys.exit()

            elif self.state == GameState.GAME_OVER:
                # Restart or menu buttons
                if SCREEN_HEIGHT//2 + 100 <= mouse_pos[1] <= SCREEN_HEIGHT//2 + 150:
                    if SCREEN_WIDTH//2 - 220 <= mouse_pos[0] <= SCREEN_WIDTH//2 - 20:
                        self.start_new_game()
                    elif SCREEN_WIDTH//2 + 20 <= mouse_pos[0] <= SCREEN_WIDTH//2 + 220:
                        self.state = GameState.MAIN_MENU

            elif self.state == GameState.SHOP:
                # Exit shop
                if SCREEN_HEIGHT - 80 <= mouse_pos[1] <= SCREEN_HEIGHT - 30:
                    if SCREEN_WIDTH//2 - 100 <= mouse_pos[0] <= SCREEN_WIDTH//2 + 100:
                        self.state = GameState.PLAYING

        elif event.type == pygame.MOUSEBUTTONUP:
            self.mouse_buttons = pygame.mouse.get_pressed()

        elif event.type == pygame.MOUSEMOTION:
            self.mouse_pos = pygame.mouse.get_pos()

# Globální instance game manageru
game_manager = GameManager()

# --- Hlavní smyčka hry ---
def main():
    """Hlavní funkce hry"""
    print("=== LIGHT OR DEAD - Pygame Community Edition ===")
    print("Initializing game...")

    running = True
    dt = 0.0
    last_time = time.time()

    # FPS counter
    fps_counter = 0
    fps_timer = 0.0
    current_fps = 0

    try:
        while running:
            current_time = time.time()
            dt = current_time - last_time
            last_time = current_time

            # Limit delta time to prevent large jumps
            dt = min(dt, 1.0 / 30.0)  # Max 30 FPS minimum

            # FPS calculation
            fps_counter += 1
            fps_timer += dt
            if fps_timer >= 1.0:
                current_fps = fps_counter
                fps_counter = 0
                fps_timer = 0.0

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                else:
                    game_manager.handle_input(event)

            # Update game
            game_manager.update(dt)

            # Render
            screen.fill(BLACK)
            game_manager.draw(screen)

            # FPS display (optional)
            if hasattr(game_manager, 'show_fps') and game_manager.show_fps:
                fps_text = small_font.render(f"FPS: {current_fps}", True, WHITE)
                screen.blit(fps_text, (10, SCREEN_HEIGHT - 30))

            # Update display
            pygame.display.flip()
            clock.tick(FPS)

    except KeyboardInterrupt:
        print("\nGame interrupted by user")
    except Exception as e:
        print(f"Game error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("Shutting down...")
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    main()
